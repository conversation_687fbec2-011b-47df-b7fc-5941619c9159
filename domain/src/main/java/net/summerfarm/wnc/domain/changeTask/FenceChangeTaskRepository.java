package net.summerfarm.wnc.domain.changeTask;

import com.github.pagehelper.PageInfo;
import net.summerfarm.wnc.common.enums.FenceChangeTaskEnums;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskPageQuery;
import net.summerfarm.wnc.common.query.changeTask.FenceChangeTaskQuery;
import net.summerfarm.wnc.domain.changeTask.entity.FenceChangeTaskEntity;

import java.util.List;

/**
 * Description:围栏切仓任务仓库接口
 * date: 2023/8/24 16:44
 *
 * <AUTHOR>
 */
public interface FenceChangeTaskRepository {

    /**
     * 分页查询切仓任务列表
     * @param fenceChangeTaskPageQuery 查询
     * @return 结果
     */
    PageInfo<FenceChangeTaskEntity> queryTaskPage(FenceChangeTaskPageQuery fenceChangeTaskPageQuery);

    /**
     * 查询切仓任务详情
     * @param changeTaskId 切仓任务ID
     * @return 切仓任务详情
     */
    FenceChangeTaskEntity queryById(Long changeTaskId);

    /**
     * 查询切仓任务集合
     * @param fenceChangeTaskQuery 查询
     * @return 切仓任务集合
     */
    List<FenceChangeTaskEntity> queryList(FenceChangeTaskQuery fenceChangeTaskQuery);

    /**
     * 查询切仓任务集合 with 区域
     * @param fenceChangeTaskQuery 查询
     * @return 切仓任务集合
     */
    List<FenceChangeTaskEntity> queryListWithArea(FenceChangeTaskQuery fenceChangeTaskQuery);

    /**
     * 保存切仓任务
     * @param fenceChangeTaskEntity 切仓任务实体
     * @return id
     */
    Long save(FenceChangeTaskEntity fenceChangeTaskEntity);

    /**
     * 更新切仓任务
     * @param fenceChangeTaskEntity 切仓任务实体
     * @return 影响行数
     */
    int update(FenceChangeTaskEntity fenceChangeTaskEntity);

    /**
     * 查询当前时间点可执行(已过执行时间)且处于指定任务状态的围栏切仓任务
     * @param status 指定任务状态
     * @param types 切仓类型
     * @return 切仓任务集合
     */
    List<FenceChangeTaskEntity> queryExecutableTask(FenceChangeTaskEnums.Status status, List<FenceChangeTaskEnums.Type> types);

    /**
     * 查询是否存在处理中的切仓任务
     * @param storeNo 城配仓编号
     * @return 任务数
     */
    long queryHandlingTasksByStoreNo(Integer storeNo);

    /**
     * 根据切仓任务ID集合查询切仓任务集合
     * @param fenceChangeTaskIds 切仓任务ID集合
     * @param status 任务状态
     * @return 切仓任务集合
     */
    List<FenceChangeTaskEntity> queryByIdsWithStatus(List<Long> fenceChangeTaskIds, Integer status);

    /**
     * 根据切仓任务ID集合更新切仓任务状态
     *
     * @param sourceFenceChangeTaskIds 切仓任务ID集合
     * @param targetStatus             目标状态
     * @param status
     */
    void updateTargetStatusByIds(List<Long> sourceFenceChangeTaskIds, Integer targetStatus, Integer status);

    /**
     * 根据切仓任务ID删除切仓任务
     * @param id 切仓任务ID
     */
    void deleteById(Long id);
}
