package net.summerfarm.wnc.domain.fence.service;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.enums.BussinessCodePrefixEnum;
import net.summerfarm.wnc.common.enums.WncCityAreaChangeWarehouseRecordsEnums;
import net.summerfarm.wnc.common.enums.WncFenceAreaChangeRecordsEnums;
import net.summerfarm.wnc.common.enums.WncFenceChangeRecordsEnums;
import net.summerfarm.wnc.common.util.SerialNumberGenerator;
import net.summerfarm.wnc.domain.fence.entity.WncFenceAreaChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.entity.WncFenceChangeRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceAreaChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.command.WncFenceChangeRecordsCommandParam;
import net.summerfarm.wnc.domain.fence.param.query.WncCityAreaChangeWarehouseRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.param.query.WncFenceChangeRecordsQueryParam;
import net.summerfarm.wnc.domain.fence.repository.*;
import net.summerfarm.wnc.domain.fence.entity.WncCityAreaChangeWarehouseRecordsEntity;
import net.summerfarm.wnc.domain.fence.param.command.WncCityAreaChangeWarehouseRecordsCommandParam;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 *
 * @Title: 城市区域切仓记录领域层
 * @Description:
 * <AUTHOR>
 * @date 2025-08-28 15:03:54
 * @version 1.0
 *
 */
@Slf4j
@Service
public class WncCityAreaChangeWarehouseRecordsCommandDomainService {


    @Autowired
    private WncCityAreaChangeWarehouseRecordsCommandRepository wncCityAreaChangeWarehouseRecordsCommandRepository;
    @Autowired
    private WncCityAreaChangeWarehouseRecordsQueryRepository wncCityAreaChangeWarehouseRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsQueryRepository wncFenceChangeRecordsQueryRepository;
    @Autowired
    private WncFenceChangeRecordsCommandRepository wncFenceChangeRecordsCommandRepository;
    @Autowired
    private SerialNumberGenerator serialNumberGenerator;
    @Autowired
    private WncFenceAreaChangeRecordsQueryRepository wncFenceAreaChangeRecordsQueryRepository;
    @Autowired
    private WncFenceAreaChangeRecordsCommandRepository wncFenceAreaChangeRecordsCommandRepository;


    public WncCityAreaChangeWarehouseRecordsEntity insert(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.insertSelective(param);
    }


    public int update(WncCityAreaChangeWarehouseRecordsCommandParam param) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return wncCityAreaChangeWarehouseRecordsCommandRepository.remove(id);
    }

    /**
     * 预约切仓时间
     * @param cityAreaChangeWarehouseRecordId 城市区域变更记录ID
     * @param preExeTime 切仓时间
     * @param fenceChangeTaskId 切仓任务ID
     */
    public void preExeTime(Long cityAreaChangeWarehouseRecordId, LocalDateTime preExeTime, Long fenceChangeTaskId) {
        if (cityAreaChangeWarehouseRecordId == null || preExeTime == null || fenceChangeTaskId == null) {
            return;
        }
        WncCityAreaChangeWarehouseRecordsEntity cityAreaChangeWarehouseRecordsEntity = wncCityAreaChangeWarehouseRecordsQueryRepository.selectById(cityAreaChangeWarehouseRecordId);
        if(cityAreaChangeWarehouseRecordsEntity == null){
            throw new BizException("城市区域切仓记录不存在，预约切仓时间失败");
        }
        if (!Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(), cityAreaChangeWarehouseRecordsEntity.getChangeStatus())) {
            throw new BizException("非待生效状态，不可预约切仓时间");
        }
        // 更新城市区域切仓记录
        WncCityAreaChangeWarehouseRecordsCommandParam commandParam = new WncCityAreaChangeWarehouseRecordsCommandParam();
        commandParam.setId(cityAreaChangeWarehouseRecordId);
        commandParam.setPreExeTime(preExeTime);
        commandParam.setFenceChangeTaskId(fenceChangeTaskId);
        wncCityAreaChangeWarehouseRecordsCommandRepository.updateSelectiveById(commandParam);

        // 更新围栏切仓记录
        wncFenceChangeRecordsCommandRepository.updateFenceChangeTaskIdByChangeBatchNo(fenceChangeTaskId, cityAreaChangeWarehouseRecordsEntity.getChangeBatchNo());

    }

    public void cancelFenceChangeTask(Long fenceChangeTaskId) {
        // 根据切仓任务找出城市区域切仓记录
        WncCityAreaChangeWarehouseRecordsQueryParam queryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        queryParam.setFenceChangeTaskId(fenceChangeTaskId);
        List<WncCityAreaChangeWarehouseRecordsEntity> cityAreaChangeWarehouseRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCondition(queryParam);

        if (CollectionUtils.isEmpty(cityAreaChangeWarehouseRecords)){
            log.info("区域切仓记录为空无需处理,fenceChangeTaskId:{}",fenceChangeTaskId);
            return;
        }

        cityAreaChangeWarehouseRecords.forEach(e -> {
            if(!Objects.equals(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(), e.getChangeStatus())){
                throw new BizException("城市区域切仓记录状态非等待生效中，不可取消");
            }
        });


        if (Objects.equals(cityAreaChangeWarehouseRecords.get(0).getAreaDefinationType(), WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.NORMAL.getValue())) {
            // 删除城市区域切仓记录
            wncCityAreaChangeWarehouseRecordsCommandRepository.deleteByFenceChangeTaskId(fenceChangeTaskId);
            // 直接删除围栏变更记录
            WncFenceChangeRecordsQueryParam param = new WncFenceChangeRecordsQueryParam();
            param.setFenceChangeTaskId(fenceChangeTaskId);
            List<WncFenceChangeRecordsEntity> wncFenceChangeRecordsList = wncFenceChangeRecordsQueryRepository.selectByCondition(param);
            if (!CollectionUtils.isEmpty(wncFenceChangeRecordsList)) {
                List<Long> wncFenceChangeRecordsIds = wncFenceChangeRecordsList.stream().map(WncFenceChangeRecordsEntity::getId).collect(Collectors.toList());
                wncFenceChangeRecordsCommandRepository.deleteByIds(wncFenceChangeRecordsIds);
                // 删除区域变更记录
                wncFenceAreaChangeRecordsCommandRepository.deleteByFenceChangeIds(wncFenceChangeRecordsIds);
            }
        }else{

            // 重置自定义区域的切仓任务，预约切仓时间置空、切仓任务ID置空、状态置为待生效
            wncCityAreaChangeWarehouseRecordsCommandRepository.resetCustomAreaFenceChangeTask(fenceChangeTaskId);

            // 置空围栏变更记录上的切仓ID
            wncFenceChangeRecordsCommandRepository.cancelFenceChangeTask(fenceChangeTaskId);
        }
    }

    public void batchInsert(List<WncCityAreaChangeWarehouseRecordsCommandParam> cityAreaChangeWarehouseRecordsList) {
        if(CollectionUtils.isEmpty(cityAreaChangeWarehouseRecordsList)){
            return;
        }
        wncCityAreaChangeWarehouseRecordsCommandRepository.batchInsert(cityAreaChangeWarehouseRecordsList);
    }

    /**
     * 新增自定义区域变更记录
     * @param province 省
     * @param city 城市
     * @param area 区域
     * @return 记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addCityAreaChangeWarehouseRecord(String province, String city, String area) {
        // 1、省市区下是否有等待生效状态的记录
        WncCityAreaChangeWarehouseRecordsQueryParam queryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        queryParam.setProvince(province);
        queryParam.setCity(city);
        queryParam.setArea(area);
        queryParam.setChangeStatus(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue());

        List<WncCityAreaChangeWarehouseRecordsEntity> existingRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCondition(queryParam);
        if (!CollectionUtils.isEmpty(existingRecords)) {
            return existingRecords.get(0).getId();
        }

        // 2、生成行政区域变更记录
        WncCityAreaChangeWarehouseRecordsCommandParam commandParam = new WncCityAreaChangeWarehouseRecordsCommandParam();
        commandParam.setProvince(province);
        commandParam.setCity(city);
        commandParam.setArea(area);
        commandParam.setChangeStatus(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue());
        commandParam.setChangeBatchNo(serialNumberGenerator.createCode(BussinessCodePrefixEnum.CHANGE_BATCH_NO));
        commandParam.setAreaDefinationType(WncCityAreaChangeWarehouseRecordsEnums.AreaDefinationType.CUSTOM.getValue());

        WncCityAreaChangeWarehouseRecordsEntity savedEntity = wncCityAreaChangeWarehouseRecordsCommandRepository.insertSelective(commandParam);
        if (null == savedEntity || null == savedEntity.getId()) {
            throw new BizException("新增行政区域变更记录失败");
        }

        // 3、查询当前省市区下是否有正常生效中的记录
        WncCityAreaChangeWarehouseRecordsQueryParam effectiveQueryParam = new WncCityAreaChangeWarehouseRecordsQueryParam();
        effectiveQueryParam.setProvince(province);
        effectiveQueryParam.setCity(city);
        effectiveQueryParam.setArea(area);
        effectiveQueryParam.setChangeStatus(WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue());

        List<WncCityAreaChangeWarehouseRecordsEntity> effectiveRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCondition(effectiveQueryParam);
        if (!CollectionUtils.isEmpty(effectiveRecords)) {
            // 4、查询正常生效中关联的自定义围栏记录
            WncCityAreaChangeWarehouseRecordsEntity effectiveRecord = effectiveRecords.get(0);
            WncFenceChangeRecordsQueryParam fenceQueryParam = new WncFenceChangeRecordsQueryParam();
            fenceQueryParam.setChangeBatchNo(effectiveRecord.getChangeBatchNo());
            fenceQueryParam.setFenceChangeStage(WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());

            List<WncFenceChangeRecordsEntity> effectiveFenceRecords = wncFenceChangeRecordsQueryRepository.selectByCondition(fenceQueryParam);
            if (!CollectionUtils.isEmpty(effectiveFenceRecords)) {
                // 5、复制围栏变更记录到新的行政区域变更记录下
                copyFenceChangeRecords(effectiveFenceRecords, savedEntity.getId(), savedEntity.getChangeBatchNo());
            }
        }

        return savedEntity.getId();
    }

    /**
     * 复制围栏变更记录到新的行政区域变更记录下
     * @param sourceFenceRecords 源围栏变更记录
     * @param newCityAreaChangeId 新的行政区域变更记录ID
     * @param newChangeBatchNo 新的变更批次号
     */
    private void copyFenceChangeRecords(List<WncFenceChangeRecordsEntity> sourceFenceRecords, Long newCityAreaChangeId, String newChangeBatchNo) {
        if (CollectionUtils.isEmpty(sourceFenceRecords)) {
            return;
        }

        // 准备批量插入的围栏变更记录（变更前和变更后各一份）
        List<WncFenceChangeRecordsCommandParam> beforeFenceRecordsToInsert = new ArrayList<>();
        List<WncFenceChangeRecordsCommandParam> afterFenceRecordsToInsert = new ArrayList<>();
        List<WncFenceAreaChangeRecordsCommandParam> areaRecordsToInsert = new ArrayList<>();

        for (WncFenceChangeRecordsEntity sourceRecord : sourceFenceRecords) {
            // 创建变更前记录
            WncFenceChangeRecordsCommandParam beforeRecord = createFenceChangeRecord(sourceRecord, newChangeBatchNo, WncFenceChangeRecordsEnums.FenceChangeStage.BEFORE.getValue());
            beforeFenceRecordsToInsert.add(beforeRecord);

            // 创建变更后记录
            WncFenceChangeRecordsCommandParam afterRecord = createFenceChangeRecord(sourceRecord, newChangeBatchNo, WncFenceChangeRecordsEnums.FenceChangeStage.AFTER.getValue());
            afterFenceRecordsToInsert.add(afterRecord);
        }

        // 逐个插入围栏变更记录以获取ID，然后创建对应的区域变更记录
        for (WncFenceChangeRecordsEntity sourceRecord : sourceFenceRecords) {
            // 查询源记录的区域变更记录
            List<WncFenceAreaChangeRecordsEntity> sourceAreaRecords = wncFenceAreaChangeRecordsQueryRepository.selectByFenceChangeIds(
                    Collections.singletonList(sourceRecord.getId()));
            // 插入变更前记录
            for (WncFenceChangeRecordsCommandParam wncFenceChangeRecordsCommandParam : beforeFenceRecordsToInsert) {
                WncFenceChangeRecordsEntity insertedBeforeRecord = wncFenceChangeRecordsCommandRepository.insertSelective(wncFenceChangeRecordsCommandParam);
                if (null == insertedBeforeRecord || null == insertedBeforeRecord.getId()) {
                    throw new BizException("新增围栏变更前记录失败");
                }
                if (!CollectionUtils.isEmpty(sourceAreaRecords)) {
                    // 为变更前记录创建区域变更记录
                    createAreaChangeRecords(sourceAreaRecords, insertedBeforeRecord.getId(), WncFenceAreaChangeRecordsEnums.FenceChangeStage.BEFORE.getValue(), areaRecordsToInsert);

                }
            }

            // 插入变更后记录
            for (WncFenceChangeRecordsCommandParam wncFenceChangeRecordsCommandParam : afterFenceRecordsToInsert) {
                WncFenceChangeRecordsEntity insertedAfterRecord = wncFenceChangeRecordsCommandRepository.insertSelective(wncFenceChangeRecordsCommandParam);
                if (null == insertedAfterRecord || null == insertedAfterRecord.getId()) {
                    throw new BizException("新增围栏变更后记录失败");
                }
                if (!CollectionUtils.isEmpty(sourceAreaRecords)) {
                    // 为变更后记录创建区域变更记录
                    createAreaChangeRecords(sourceAreaRecords, insertedAfterRecord.getId(), WncFenceAreaChangeRecordsEnums.FenceChangeStage.AFTER.getValue(), areaRecordsToInsert);

                }
            }
        }

        // 批量插入区域变更记录
        if (!CollectionUtils.isEmpty(areaRecordsToInsert)) {
            wncFenceAreaChangeRecordsCommandRepository.batchInsert(areaRecordsToInsert);
        }
    }

    /**
     * 创建围栏变更记录
     * @param sourceRecord 源记录
     * @param newChangeBatchNo 新的变更批次号
     * @param fenceChangeStage 围栏变更阶段
     * @return 围栏变更记录参数
     */
    private WncFenceChangeRecordsCommandParam createFenceChangeRecord(WncFenceChangeRecordsEntity sourceRecord, String newChangeBatchNo, Integer fenceChangeStage) {
        WncFenceChangeRecordsCommandParam commandParam = new WncFenceChangeRecordsCommandParam();
        commandParam.setFenceId(sourceRecord.getFenceId());
        commandParam.setFenceStoreNo(sourceRecord.getFenceStoreNo());
        commandParam.setFenceAreaNo(sourceRecord.getFenceAreaNo());
        commandParam.setFenceMsg(sourceRecord.getFenceDetailEntity() != null ? JSON.toJSONString(sourceRecord.getFenceDetailEntity()) : null);
        commandParam.setChangeBatchNo(newChangeBatchNo);
        commandParam.setFenceChangeStage(fenceChangeStage);
        commandParam.setFenceStoreName(sourceRecord.getFenceStoreName());
        commandParam.setFenceAreaName(sourceRecord.getFenceAreaName());
        commandParam.setFenceWarehouseNos(sourceRecord.getFenceWarehouseNos());
        commandParam.setFenceWarehouseNames(sourceRecord.getFenceWarehouseNames());
        commandParam.setFenceName(sourceRecord.getFenceName());
        commandParam.setCreateTime(LocalDateTime.now());
        return commandParam;
    }

    /**
     * 创建区域变更记录
     * @param sourceAreaRecords 源区域记录
     * @param fenceChangeId 围栏变更记录ID
     * @param fenceChangeStage 围栏变更阶段
     * @param areaRecordsToInsert 待插入的区域记录列表
     */
    private void createAreaChangeRecords(List<WncFenceAreaChangeRecordsEntity> sourceAreaRecords, Long fenceChangeId, Integer fenceChangeStage, List<WncFenceAreaChangeRecordsCommandParam> areaRecordsToInsert) {
        for (WncFenceAreaChangeRecordsEntity sourceAreaRecord : sourceAreaRecords) {
            WncFenceAreaChangeRecordsCommandParam areaCommandParam = new WncFenceAreaChangeRecordsCommandParam();
            areaCommandParam.setFenceChangeId(fenceChangeId);
            areaCommandParam.setFenceId(sourceAreaRecord.getFenceId());
            areaCommandParam.setCity(sourceAreaRecord.getCity());
            areaCommandParam.setArea(sourceAreaRecord.getArea());
            areaCommandParam.setAdCodeMsgId(sourceAreaRecord.getAdCodeMsgId());
            areaCommandParam.setCustomAreaName(sourceAreaRecord.getCustomAreaName());
            areaCommandParam.setAdCodeMsgDetail(sourceAreaRecord.getAdCodeMsgDetailEntity() != null ? JSON.toJSONString(sourceAreaRecord.getAdCodeMsgDetailEntity()) : null);
            areaCommandParam.setGeoShape(sourceAreaRecord.getGeoShape());
            areaCommandParam.setFenceChangeStage(fenceChangeStage);
            areaCommandParam.setAreaDrawType(sourceAreaRecord.getAreaDrawType());
            areaCommandParam.setCreateTime(LocalDateTime.now());
            areaRecordsToInsert.add(areaCommandParam);
        }
    }

    /**
     * 待生效状态变更为生效中状态
     * @param cityAreaChangeIds 城市区域切仓记录ID集合
     */
    public void waitChangeToEffectiveStatus(List<Long> cityAreaChangeIds) {
        if(CollectionUtils.isEmpty(cityAreaChangeIds)){
            return;
        }
        // 生效时间
        LocalDateTime effectiveTime = LocalDateTime.now();

        wncCityAreaChangeWarehouseRecordsCommandRepository.updateChangeStatusWithEffectiveTime(cityAreaChangeIds,
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.WAIT.getValue(),
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue(),
                effectiveTime);

    }

    /**
     * 根据城市区域将状态变由有效更为已结束
     * @param city 城市
     * @param areas 围栏区域
     */
    public void effectiveToEndStatusByCityAreas(String city, List<String> areas) {
        if(StringUtils.isEmpty(city)){
            return;
        }
        List<WncCityAreaChangeWarehouseRecordsEntity> effectiveCityAreaChangeWarehouseRecords = wncCityAreaChangeWarehouseRecordsQueryRepository.selectByCityAreasChangeStage(city,
                areas,
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE);

        if (CollectionUtils.isEmpty(effectiveCityAreaChangeWarehouseRecords)) {
            return;
        }

        List<Long> ids = effectiveCityAreaChangeWarehouseRecords.stream().map(WncCityAreaChangeWarehouseRecordsEntity::getId).collect(Collectors.toList());
        // 变更城市区域记录状态为已结束
        wncCityAreaChangeWarehouseRecordsCommandRepository.updateChangeStatusWithOverTime(ids,
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.EFFECTIVE.getValue(),
                WncCityAreaChangeWarehouseRecordsEnums.ChangeStatus.END.getValue(),
                LocalDateTime.now());
    }
}
