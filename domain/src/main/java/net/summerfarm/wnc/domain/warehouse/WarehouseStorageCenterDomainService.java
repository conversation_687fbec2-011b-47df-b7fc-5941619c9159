package net.summerfarm.wnc.domain.warehouse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.common.config.WncConfig;
import net.summerfarm.wnc.common.enums.ContactConfigEnums;
import net.summerfarm.wnc.common.enums.FenceEnums;
import net.summerfarm.wnc.common.enums.WncTenantGlobalFenceRuleEnums;
import net.summerfarm.wnc.common.enums.WncWarehouseStorageFenceRuleEnums;
import net.summerfarm.wnc.common.query.fence.WncWarehouseStorageFenceRuleQuery;
import net.summerfarm.wnc.common.query.warehouse.*;
import net.summerfarm.wnc.common.util.DistanceUtil;
import net.summerfarm.wnc.domain.config.repository.ContactConfigQueryRepository;
import net.summerfarm.wnc.domain.config.entity.ContactConfigEntity;
import net.summerfarm.wnc.domain.fence.DeliveryFenceDomainService;
import net.summerfarm.wnc.domain.fence.DeliveryFenceRepository;
import net.summerfarm.wnc.domain.fence.WncTenantGlobalFenceRuleRepository;
import net.summerfarm.wnc.domain.fence.WncWarehouseStorageFenceRuleRepository;
import net.summerfarm.wnc.domain.fence.entity.AdCodeMsgEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceChannelBusinessWhiteConfigEntity;
import net.summerfarm.wnc.domain.fence.entity.FenceEntity;
import net.summerfarm.wnc.domain.fence.service.FenceChannelBusinessWhiteConfigQueryDomainService;
import net.summerfarm.wnc.domain.warehouse.entity.*;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/3/28 17:35<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WarehouseStorageCenterDomainService {

    private final WarehouseStorageCenterRepository warehouseStorageCenterRepository;
    private final WncWarehouseStorageFenceRepository rcWarehouseStorageFenceRepository;
    private final WarehouseStorageCenterBusinessRepository warehouseStorageCenterBusinessRepository;
    private final WarehouseTakeStandardRepository warehouseTakeStandardRepository;
    private final WarehouseLogisticsMappingRepository warehouseLogisticsMappingRepository;
    @Lazy
    private final DeliveryFenceRepository deliveryFenceRepository;
    private final WncWarehouseStorageTenantRepository wncWarehouseStorageTenantRepository;
    private final WarehouseInventoryMappingRepository warehouseInventoryMappingRepository;
    private final ContactConfigQueryRepository contactConfigQueryRepository;
    private final WncTenantGlobalFenceRuleRepository wncTenantGlobalFenceRuleRepository;
    private final WncWarehouseStorageFenceRuleRepository wncWarehouseStorageFenceRuleRepository;
    private final FenceChannelBusinessWhiteConfigQueryDomainService fenceChannelBusinessWhiteConfigQueryDomainService;
    private final WncConfig wncConfig;
    private final DeliveryFenceDomainService deliveryFenceDomainService;



    /**
     * 保存信息
     * @param warehouseStorageEntity 保存实体
     */
    public void save(WarehouseStorageEntity warehouseStorageEntity) {
        //库存仓名称校验
        cheakTenantWarehouseName(warehouseStorageEntity);
        //保存
        warehouseStorageCenterRepository.save(warehouseStorageEntity);

    }

    /**
     * 更新信息
     * @param warehouseStorageEntity 更新实体
     */
    public void update(WarehouseStorageEntity warehouseStorageEntity) {
        //更新
        warehouseStorageCenterRepository.update(warehouseStorageEntity);
    }

    /**
     * 同一个商户是否存在相同的库存仓名称
     * @param warehouseStorageEntity 仓库信息
     */
    private void cheakTenantWarehouseName(WarehouseStorageEntity warehouseStorageEntity) {
        //判断相同租户是否存在相同仓库名称
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryWarehouseStorage(WarehouseStorageQuery.builder()
                .tenantId(warehouseStorageEntity.getTenantId())
                .warehouseName(warehouseStorageEntity.getWarehouseName())
                .build()
        );
        if(!CollectionUtils.isEmpty(warehouseStorageEntities)){
            throw new BizException("此商户存在相同的库存仓名称");
        }
    }


    /**
     * 批量查询仓库详情
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    public List<WarehouseStorageEntity> queryDetailList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WarehouseStorageEntity> warehouseStorageEntities = warehouseStorageCenterRepository.queryList(warehouseStorageQuery);

        if(CollectionUtils.isEmpty(warehouseStorageEntities)){
            return Collections.EMPTY_LIST;
        }

        List<Integer> warehouseNos = warehouseStorageEntities.stream().map(WarehouseStorageEntity::getWarehouseNo).collect(Collectors.toList());

        //查询产能
        List<WarehouseStorageCenterBusEntity> warehouseStorageCenterBusEntities = warehouseStorageCenterBusinessRepository.queryList(WarehouseBusinessQuery.builder()
                .warehouseNos(warehouseNos)
                .build());
        //查询收货标准信息
        List<WarehouseTakeStandardEntity> warehouseTakeStandardEntities = warehouseTakeStandardRepository.queryList(WarehouseTakeStandardQuery.builder()
                .warehouseNos(warehouseNos)
                .build());
        //查询围栏
        List<WncWarehouseStorageFenceEntity> rcWarehouseStorageFenceEntities = rcWarehouseStorageFenceRepository.queryList(WarehouseStorageFenceQuery.builder()
                .warehouseNos(warehouseNos)
                .build());

        Map<Integer, List<WarehouseStorageCenterBusEntity>> warehouseBusListMap = warehouseStorageCenterBusEntities.stream().collect(Collectors.groupingBy(WarehouseStorageCenterBusEntity::getWarehouseNo));
        Map<Integer, List<WarehouseTakeStandardEntity>> warehouseTakeStandardListMap = warehouseTakeStandardEntities.stream().collect(Collectors.groupingBy(WarehouseTakeStandardEntity::getWarehouseNo));
        Map<Integer, List<WncWarehouseStorageFenceEntity>> rcWarehouseStorageFenceListMap = rcWarehouseStorageFenceEntities.stream().collect(Collectors.groupingBy(WncWarehouseStorageFenceEntity::getWarehouseNo));

        warehouseStorageEntities.forEach(warehouseStorageEntity -> {
            if(!CollectionUtils.isEmpty(warehouseBusListMap.get(warehouseStorageEntity.getWarehouseNo()))){
                warehouseStorageEntity.setWarehouseStorageCenterBusEntity(warehouseBusListMap.get(warehouseStorageEntity.getWarehouseNo()).get(0));
            }
            if(!CollectionUtils.isEmpty(warehouseTakeStandardListMap.get(warehouseStorageEntity.getWarehouseNo()))){
                warehouseStorageEntity.setWarehouseTakeStandardEntities(warehouseTakeStandardListMap.get(warehouseStorageEntity.getWarehouseNo()));
            }
            if(!CollectionUtils.isEmpty(rcWarehouseStorageFenceListMap.get(warehouseStorageEntity.getWarehouseNo()))){
                warehouseStorageEntity.setWarehouseStorageFenceEntities(rcWarehouseStorageFenceListMap.get(warehouseStorageEntity.getWarehouseNo()));
            }
        });

        return warehouseStorageEntities;
    }

    /**
     * 查询自营仓仓库信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    public List<WarehouseStorageEntity> querySelfWarehouseStorageFence(WarehouseStorageQuery warehouseStorageQuery) {
        //根据市区和租户ID查询围栏信息
        List<WncWarehouseStorageFenceEntity> wncWarehouseStorageFenceEntities = rcWarehouseStorageFenceRepository.queryList(WarehouseStorageFenceQuery.builder()
                .tenantId(warehouseStorageQuery.getTenantId())
                .citys(Collections.singletonList(warehouseStorageQuery.getCity()))
                .areas(Collections.singletonList(warehouseStorageQuery.getArea()))
                .build());
        if(CollectionUtils.isEmpty(wncWarehouseStorageFenceEntities)){
            return new ArrayList<>();
        }
        List<Integer> warehouseNoList = wncWarehouseStorageFenceEntities.stream().map(WncWarehouseStorageFenceEntity::getWarehouseNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(warehouseNoList)){
            return new ArrayList<>();
        }

        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder()
                .status(warehouseStorageQuery.getStatus())
                .warehouseNos(warehouseNoList)
                .build());

        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntityList) {
            warehouseStorageEntity.setTenantId(warehouseStorageQuery.getTenantId());
            String poiNote = warehouseStorageEntity.getPoiNote();
            if(StringUtils.isNotBlank(poiNote) && StringUtils.isNotBlank(warehouseStorageQuery.getPoi())){
                try {
                    warehouseStorageEntity.setDistance(BigDecimal.valueOf(DistanceUtil.getPoiDistance(poiNote, warehouseStorageQuery.getPoi())).setScale(2,BigDecimal.ROUND_HALF_UP));
                } catch (Exception e) {
                    log.error("计算到仓库距离异常",e);
                }
            }else{
                warehouseStorageEntity.setDistance(new BigDecimal(0));
            }
        }

        return warehouseStorageEntityList;
    }

    /**
     * 查询代仓仓库信息
     * @param warehouseStorageQuery 查询
     * @return 结果
     */
    public List<WarehouseStorageEntity> queryProxyWarehouseFence(WarehouseStorageQuery warehouseStorageQuery) {
        FenceEntity fenceEntity = null;
        //判断是否传联系人ID/门店ID 未传走原逻辑
        Long tenantId = warehouseStorageQuery.getTenantId();
        if (warehouseStorageQuery.getContactId() != null){
            //传联系人ID/门店ID优先判断 门店是否存在绑定的城配仓 不存在走原逻辑
            ContactConfigEntity contactConfigEntity = contactConfigQueryRepository.queryByUk(ContactConfigEnums.Source.getSourceByTenantId(tenantId), warehouseStorageQuery.getContactId());
            if (contactConfigEntity != null){
                log.info("SAAS-柠季下单鲜沐直供链路，门店ID：{}，绑定城配仓编号：{}", warehouseStorageQuery.getContactId(), contactConfigEntity.getStoreNo());
                fenceEntity = new FenceEntity();
                fenceEntity.setStoreNo(contactConfigEntity.getStoreNo());
                fenceEntity.setOrderChannelType(FenceEnums.OrderChannelType.getAllValueSplit());
            }
        }
        if (fenceEntity == null){
            //根据仓库和城市和区域查询符合的仓库信息
            fenceEntity = deliveryFenceDomainService.queryFenceByAddressStatusWithPoi(warehouseStorageQuery.getCity(), warehouseStorageQuery.getArea(), warehouseStorageQuery.getPoi(),null,null);
        }

        if(fenceEntity == null || fenceEntity.getStoreNo() == null){
            return new ArrayList<>();
        }
        //围栏是否开放Saas下单
        if(fenceEntity.getOrderChannelType() != null && !fenceEntity.checkOrderChannelType(FenceEnums.OrderChannelType.SAAS.getValue())){
            return new ArrayList<>();
        }
        // 围栏渠道白名单开关
        if(wncConfig.isFenceChannelBusinessWhiteConfigOpen()){
            // 围栏ID是空说明指定了城配仓,只有围栏ID不为null的时候才需要校验围栏渠道
            if(fenceEntity.getId() != null){
                List<FenceChannelBusinessWhiteConfigEntity> fenceChannelBusinessWhiteConfigEntities = fenceChannelBusinessWhiteConfigQueryDomainService.queryChannelBusinessWhiteConfig(fenceEntity.getId(),tenantId, null);
                if(CollectionUtils.isEmpty(fenceChannelBusinessWhiteConfigEntities)){
                    return new ArrayList<>();
                }
            }
        }
        //根据sku和城配仓编号查询对应的城配配送信息
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryList(WarehouseInventoryMappingQuery.builder()
                .skus(warehouseStorageQuery.getSkuList())
                .storeNo(fenceEntity.getStoreNo())
                .build());
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            return new ArrayList<>();
        }

        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder()
                .warehouseNos(warehouseInventoryMappingEntities.stream().map(WarehouseInventoryMappingEntity::getWarehouseNo).collect(Collectors.toList()))
                .status(warehouseStorageQuery.getStatus())
                .build());
        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntityList) {
            String poiNote = warehouseStorageEntity.getPoiNote();
            warehouseStorageEntity.setStoreNoList(Collections.singletonList(fenceEntity.getStoreNo()));
            if(StringUtils.isNotBlank(poiNote) && StringUtils.isNotBlank(warehouseStorageQuery.getPoi())){
                try {
                    warehouseStorageEntity.setDistance(BigDecimal.valueOf(DistanceUtil.getPoiDistance(poiNote, warehouseStorageQuery.getPoi())).setScale(2,BigDecimal.ROUND_HALF_UP));
                } catch (Exception e) {
                    log.error("计算到仓库距离异常",e);
                }
            }else{
                warehouseStorageEntity.setDistance(new BigDecimal(0));
            }
        }

        return warehouseStorageEntityList;
    }

    public List<WarehouseStorageEntity> querySelffList(WarehouseStorageQuery warehouseStorageQuery) {
        return warehouseStorageCenterRepository.queryList(warehouseStorageQuery);
    }

    public List<WarehouseStorageEntity> queryProxyList(WarehouseStorageQuery warehouseStorageQuery) {
        List<WncWarehouseStorageTenantEntity> wncWarehouseStorageTenantEntities = wncWarehouseStorageTenantRepository.queryList(WncWarehouseStorageTenantQuery.builder()
                .tenantId(warehouseStorageQuery.getTenantId())
                .build());

        List<Long> warehouseNos = wncWarehouseStorageTenantEntities.stream().map(WncWarehouseStorageTenantEntity::getWarehouseNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(warehouseNos)){
            return new ArrayList<>();
        }

        return warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder()
                .warehouseNos(JSONArray.parseArray(warehouseNos.toString(), Integer.class))
                .status(warehouseStorageQuery.getStatus())
                .build());
    }

    public List<WarehouseStorageEntity> querySkuProxyFenceList(WarehouseStorageQuery warehouseStorageQuery) {
        //查询映射关系
        List<WarehouseInventoryMappingEntity> warehouseInventoryMappingEntities = warehouseInventoryMappingRepository.queryValideStoreWarehouseNoMappingBySkuWarehouseNos(Collections.singletonList(warehouseStorageQuery.getSku()),warehouseStorageQuery.getWarehouseNos());
        //查询仓库信息
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder().warehouseNos(warehouseStorageQuery.getWarehouseNos()).build());
        if(CollectionUtils.isEmpty(warehouseInventoryMappingEntities)){
            warehouseStorageEntityList.forEach(warehouseStorageEntity -> warehouseStorageEntity.setSku(warehouseStorageQuery.getSku()));
            return warehouseStorageEntityList;
        }

        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        //根据城配仓查询围栏信息
        Map<Integer, List<WarehouseInventoryMappingEntity>> warehouseStorageListMap = warehouseInventoryMappingEntities.stream().collect(Collectors.groupingBy(WarehouseInventoryMappingEntity::getWarehouseNo));
        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntityList) {
            warehouseStorageEntity.setSku(warehouseStorageQuery.getSku());
            if(warehouseStorageListMap.get(warehouseStorageEntity.getWarehouseNo()) != null){
                List<Integer> storeNoList = warehouseStorageListMap.get(warehouseStorageEntity.getWarehouseNo()).stream().map(WarehouseInventoryMappingEntity::getStoreNo).collect(Collectors.toList());
                List<FenceEntity> fenceEntities = deliveryFenceRepository.queryFenceByStoreNoList(storeNoList);
                if(!CollectionUtils.isEmpty(fenceEntities)){
                    List<AdCodeMsgEntity> adCodeMsgEntities = fenceEntities.stream().filter(fenceEntity -> !CollectionUtils.isEmpty(fenceEntity.getAdCodeMsgEntities())).map(FenceEntity::getAdCodeMsgEntities).flatMap(Collection::stream).collect(Collectors.toList());
                    List<WncWarehouseStorageFenceEntity> warehouseStorageFenceEntities = new ArrayList<>();
                    for (AdCodeMsgEntity adCodeMsgEntity : adCodeMsgEntities) {
                        WncWarehouseStorageFenceEntity wncWarehouseStorageFenceEntity = new WncWarehouseStorageFenceEntity();
                        wncWarehouseStorageFenceEntity.setProvince(adCodeMsgEntity.getProvince());
                        wncWarehouseStorageFenceEntity.setCity(adCodeMsgEntity.getCity());
                        wncWarehouseStorageFenceEntity.setArea(adCodeMsgEntity.getArea());

                        warehouseStorageFenceEntities.add(wncWarehouseStorageFenceEntity);
                    }
                    warehouseStorageEntity.setWarehouseStorageFenceEntities(warehouseStorageFenceEntities);
                }
            }
            warehouseStorageEntities.add(warehouseStorageEntity);
        }

        return warehouseStorageEntities;
    }

    public List<WarehouseStorageEntity> querySkuSelfFenceList(WarehouseStorageQuery warehouseStorageQuery) {
        //查询仓库信息
        List<WarehouseStorageEntity> warehouseStorageEntityList = warehouseStorageCenterRepository.queryList(WarehouseStorageQuery.builder().warehouseNos(warehouseStorageQuery.getWarehouseNos()).build());

        List<WncWarehouseStorageFenceEntity> wncWarehouseStorageFenceEntities = rcWarehouseStorageFenceRepository.queryList(WarehouseStorageFenceQuery.builder().warehouseNos(warehouseStorageQuery.getWarehouseNos()).build());
        if(CollectionUtils.isEmpty(wncWarehouseStorageFenceEntities)){
            warehouseStorageEntityList.forEach(warehouseStorageEntity -> warehouseStorageEntity.setSku(warehouseStorageQuery.getSku()));
            return warehouseStorageEntityList;
        }
        List<WarehouseStorageEntity> warehouseStorageEntities = new ArrayList<>();
        Map<Integer, List<WncWarehouseStorageFenceEntity>> warehouseNoStorageMap = wncWarehouseStorageFenceEntities.stream().collect(Collectors.groupingBy(WncWarehouseStorageFenceEntity::getWarehouseNo));

        for (WarehouseStorageEntity warehouseStorageEntity : warehouseStorageEntityList) {
            warehouseStorageEntity.setWarehouseStorageFenceEntities(warehouseNoStorageMap.get(warehouseStorageEntity.getWarehouseNo()));
            warehouseStorageEntity.setSku(warehouseStorageQuery.getSku());
            warehouseStorageEntities.add(warehouseStorageEntity);
        }

        return warehouseStorageEntities;
    }

    /**
     * 仓库排序处理器
     * @param warehouseStorageEntities 自营仓
     * @param proxyWarehouseStorageEntities 代仓
     * @param wncWarehouseStorageFenceRuleQuery 查询条件
     * @return 排序规则
     */
    public List<WarehouseStorageEntity> warehouseSortHandle(List<WarehouseStorageEntity> warehouseStorageEntities, List<WarehouseStorageEntity> proxyWarehouseStorageEntities, WncWarehouseStorageFenceRuleQuery wncWarehouseStorageFenceRuleQuery) {
        WncTenantGlobalFenceRuleEntity wncTenantGlobalFenceRuleEntity = wncTenantGlobalFenceRuleRepository.queryUk(wncWarehouseStorageFenceRuleQuery.getTenantId());
        Integer globalDeliveryRule = wncTenantGlobalFenceRuleEntity == null ?
                WncTenantGlobalFenceRuleEnums.deliveryRule.SELF_FIRST.getValue() : wncTenantGlobalFenceRuleEntity.getGlobalDeliveryRule();
        //查询配送规则
        WncWarehouseStorageFenceRuleEntity wncWarehouseStorageFenceRuleEntity = wncWarehouseStorageFenceRuleRepository.queryByUk(wncWarehouseStorageFenceRuleQuery);
        log.info("全局配置规则:{}", JSON.toJSONString(wncTenantGlobalFenceRuleEntity));
        log.info("区域配置规则:{}", JSON.toJSONString(wncWarehouseStorageFenceRuleEntity));

        List<WarehouseStorageEntity> sortWarehouseList = new ArrayList<>();
        if(wncWarehouseStorageFenceRuleEntity == null){
            //自营仓优先
            if(Objects.equals(WncTenantGlobalFenceRuleEnums.deliveryRule.SELF_FIRST.getValue(), globalDeliveryRule)){
                sortWarehouseList.addAll(warehouseStorageEntities);
                sortWarehouseList.addAll(proxyWarehouseStorageEntities);
            }else{
                //三方仓优先
                sortWarehouseList.addAll(proxyWarehouseStorageEntities);
                sortWarehouseList.addAll(warehouseStorageEntities);
            }
        }else{
            //存在自营仓冲突
            Integer deliveryRule = wncWarehouseStorageFenceRuleEntity.getDeliveryRule();
            //自营仓优先
            if(Objects.equals(WncTenantGlobalFenceRuleEnums.deliveryRule.SELF_FIRST.getValue(), globalDeliveryRule)){
                if(Objects.equals(WncWarehouseStorageFenceRuleEnums.deliveryRule.DISTANCE_FIRST.getValue(),deliveryRule)){
                    //距离优先
                    warehouseStorageEntities.sort(Comparator.comparing(WarehouseStorageEntity::getDistance,Comparator.nullsLast(BigDecimal::compareTo)));
                    proxyWarehouseStorageEntities.sort(Comparator.comparing(WarehouseStorageEntity::getDistance,Comparator.nullsLast(BigDecimal::compareTo)));
                    sortWarehouseList.addAll(warehouseStorageEntities);
                    sortWarehouseList.addAll(proxyWarehouseStorageEntities);
                }else{
                    //手动优先
                    List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList();
                    List<Integer> warehouseNoList = conflictWarehouseJsonEntityList.stream().map(ConflictWarehouseJsonEntity::getWarehouseNo).collect(Collectors.toList());
                    Map<Integer, WarehouseStorageEntity> warehouseMap = warehouseStorageEntities.stream()
                            .collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity()));

                    warehouseNoList.forEach(warehouseNo->{
                        if(warehouseMap.get(warehouseNo) != null){
                            sortWarehouseList.add(warehouseMap.get(warehouseNo));
                        }
                    });
                    sortWarehouseList.addAll(proxyWarehouseStorageEntities);
                }
            }else{
                //三方仓优先
                if(Objects.equals(WncWarehouseStorageFenceRuleEnums.deliveryRule.DISTANCE_FIRST.getValue(),deliveryRule)){
                    //距离优先
                    proxyWarehouseStorageEntities.sort(Comparator.comparing(WarehouseStorageEntity::getDistance,Comparator.nullsLast(BigDecimal::compareTo)));
                    warehouseStorageEntities.sort(Comparator.comparing(WarehouseStorageEntity::getDistance,Comparator.nullsLast(BigDecimal::compareTo)));
                    sortWarehouseList.addAll(proxyWarehouseStorageEntities);
                    sortWarehouseList.addAll(warehouseStorageEntities);
                }else{
                    //手动优先
                    //先三方仓优先
                    sortWarehouseList.addAll(proxyWarehouseStorageEntities);

                    List<ConflictWarehouseJsonEntity> conflictWarehouseJsonEntityList = wncWarehouseStorageFenceRuleEntity.getConflictWarehouseJsonEntityList();
                    List<Integer> warehouseNoList = conflictWarehouseJsonEntityList.stream().map(ConflictWarehouseJsonEntity::getWarehouseNo).collect(Collectors.toList());
                    Map<Integer, WarehouseStorageEntity> warehouseMap = warehouseStorageEntities.stream()
                            .collect(Collectors.toMap(WarehouseStorageEntity::getWarehouseNo, Function.identity()));

                    warehouseNoList.forEach(warehouseNo->{
                        if(warehouseMap.get(warehouseNo) != null){
                            sortWarehouseList.add(warehouseMap.get(warehouseNo));
                        }
                    });
                }
            }
        }
        return sortWarehouseList;
    }

    /**
     * 保存鲜沐仓库信息
     * @param warehouseStorageEntity 保存
     */
    public void saveXmWarehouse(WarehouseStorageEntity warehouseStorageEntity) {
        //保存
        warehouseStorageCenterRepository.saveXmWarehouse(warehouseStorageEntity);

    }

    /**
     * 处理城配仓和库存仓的映射关系
     * @param storeNoList 城配仓集合
     * @return
     */
    public Map<Integer, List<WarehouseStorageEntity>> handleLogisticsMapping(List<Integer> storeNoList) {
        if(CollectionUtils.isEmpty(storeNoList)){
            return null;
        }
        List<WarehouseLogisticsMappingEntity> mappingEntities = warehouseLogisticsMappingRepository.queryWithValidWarehouseMapping(storeNoList);

        return mappingEntities.stream().collect(Collectors
                .groupingBy(WarehouseLogisticsMappingEntity::getStoreNo,
                        Collectors.mapping(WarehouseLogisticsMappingEntity::getWarehouseStorageEntity, Collectors.toList())));
    }

    /**
     * 更新库存仓相关信息
     * @param warehouseStorageEntity 库存仓相关
     */
    public void updateStorage(WarehouseStorageEntity warehouseStorageEntity) {
        //更新
        warehouseStorageCenterRepository.updateStorage(warehouseStorageEntity);
    }
}
